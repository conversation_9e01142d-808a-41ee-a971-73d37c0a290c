using Application.Abstractions.Data;
using Domain.Jobs;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using SharedKernel;
using Web.Api.Extensions;
using Web.Api.Infrastructure;

namespace Web.Api.Endpoints.Jobs;

internal sealed class UpdateJob : IEndpoint
{
    public void MapEndpoint(IEndpointRouteBuilder app)
    {
        app.MapPut("jobs/{id:guid}", UpdateJobAsync)
            .WithTags(Tags.Jobs)
            .WithName("UpdateJob")
            .Produces<Guid>()
            .ProducesValidationProblem()
            .RequireAuthorization();
    }

    private static async Task<IResult> UpdateJobAsync(
        Guid id,
        UpdateJobRequest request,
        IApplicationDbContext context,
        CancellationToken cancellationToken)
    {
        Job? job = await context.Jobs
            .FirstOrDefaultAsync(j => j.Id == id, cancellationToken);

        if (job is null)
        {
            return Results.NotFound();
        }

        // Update job properties to trigger audit
        job.JobTitle = request.JobTitle;
        job.JobDescription = request.JobDescription;
        job.JobPostingUrl = request.JobPostingUrl.AbsolutePath;
        job.CompanyUrl = request.CompanyUrl.AbsolutePath;
        job.AppliedAt = request.AppliedAt;

        await context.SaveChangesAsync(cancellationToken);

        return Results.Ok(job.Id);
    }
}

public sealed record UpdateJobRequest(
    string JobTitle,
    string JobDescription,
    Uri JobPostingUrl,
    Uri CompanyUrl,
    DateTime? AppliedAt);
