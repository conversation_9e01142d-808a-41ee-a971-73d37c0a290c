﻿using Domain.Jobs;
using Domain.Resumes;
using Domain.Todos;
using Domain.Users;
using Microsoft.EntityFrameworkCore;

namespace Application.Abstractions.Data;

public interface IApplicationDbContext
{
    DbSet<User> Users { get; }
    DbSet<TodoItem> TodoItems { get; }
    DbSet<Job> Jobs { get; }
    DbSet<Resume> Resumes { get; }

    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
}
